define([
    'mage/utils/wrapper',
    'Magento_Customer/js/customer-data',
    'Magento_Customer/js/section-config',
    'mage/url'
], function (wrapper, customerData, sectionConfig, urlBuilder) {
    'use strict';

    return function (lacRedirect) {
        return wrapper.wrap(lacRedirect, function (lacRedirect, config) {
            customerData.reload(sectionConfig.getSectionNames()).done(function (result) {
                window.location.href = result.customer?.isSeller ?
                    urlBuilder.build('marketplace/account/dashboard') :
                    config.redirectUrl;
            });
        });
    };
});
