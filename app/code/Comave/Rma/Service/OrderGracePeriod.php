<?php

declare(strict_types=1);

namespace Comave\Rma\Service;

use Comave\Rma\Model\ConfigProvider;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\ShipmentInterface;
use Magento\Sales\Model\ResourceModel\Order\Shipment\Item\CollectionFactory;
use Psr\Log\LoggerInterface;

class OrderGracePeriod
{
    /**
     * @var array|ShipmentInterface[]
     */
    private array $shipmentItems = [];

    /**
     * @param SellerReturnAddressProvider $addressProvider
     * @param ConfigProvider $configProvider
     * @param TimezoneInterface $dateTime
     * @param LoggerInterface $logger
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        private readonly SellerReturnAddressProvider $addressProvider,
        private readonly ConfigProvider $configProvider,
        private readonly TimezoneInterface $dateTime,
        private readonly LoggerInterface $logger,
        private readonly CollectionFactory $collectionFactory
    ) {
    }

    /**
     * @param OrderInterface $order
     * @param int|string $orderItemId
     * @return bool
     * @throws \Exception
     */
    public function isAllowed(OrderInterface $order, int|string $orderItemId): bool
    {
        if ($order->getIsVirtual()) {
            return true;
        }

        $shippingAddress = $order->getShippingAddress();

        if ($shippingAddress === null) {
            throw new LocalizedException(
                __(
                    'Unable to determine order shipping address for order #%1',
                    $order->getIncrementId()
                )
            );
        }

        $sellerReturnAddresses = $this->addressProvider->get($order);

        if (empty($sellerReturnAddresses) || !isset($sellerReturnAddresses[$orderItemId])) {
            throw new LocalizedException(__('Unable to determine seller origin country'));
        }

        $sellerReturnAddress = $sellerReturnAddresses[$orderItemId];
        $sellerCountryId = $sellerReturnAddress['country_id'] ?? false;

        if (empty($sellerCountryId)) {
            throw new LocalizedException(
                __(
                    'Unable to determine seller origin country for address %1, seller ID %2',
                    $sellerReturnAddress['entity_id'],
                    $sellerReturnAddress['parent_id']
                )
            );
        }

        $orderCountryId = $shippingAddress->getCountryId();
        $websiteId = $order->getStore()->getWebsiteId();
        $gracePeriod = $sellerCountryId === $orderCountryId ?
            $this->configProvider->getLocalGracePeriod((int) $websiteId) :
            $this->configProvider->getInternationalGracePeriod((int) $websiteId);

        $lastShipmentDate = $this->getLastShipment(
            (int) $order->getId(),
            (int) $orderItemId
        );

        if (empty($lastShipmentDate)) {
            $this->logger->notice(
                '[ComaveRma] Shipment not found for item, return false',
                [
                    'order_id' => $order->getId(),
                    'order_item_id' => $orderItemId,
                ]
            );

            return false;
        }

        $currentDate = $this->dateTime->date();
        $gracePeriodDate = $this->dateTime->date(
            strtotime($lastShipmentDate)
        )->add(
            new \DateInterval(sprintf('P%dD', $gracePeriod))
        );

        return $currentDate->getTimestamp() <= $gracePeriodDate->getTimestamp();
    }

    /**
     * @param int $orderId
     * @param int $orderItemId
     * @return string|null
     */
    private function getLastShipment(int $orderId, int $orderItemId): ?string
    {
        if (isset($this->shipmentItems[$orderItemId])) {
            return $this->shipmentItems[$orderItemId][ShipmentInterface::CREATED_AT] ?? null;
        }

        $collection = $this->collectionFactory->create();
        $collection->getSelect()
            ->reset(Select::COLUMNS)
            ->joinLeft(
                ['s' => $collection->getTable('sales_shipment')],
                'main_table.parent_id = s.entity_id',
                [
                    'main_table.order_item_id',
                    's.' . ShipmentInterface::CREATED_AT
                ]
            )->where(
                's.order_id = ?',
                $orderId
            )->order('s.' . ShipmentInterface::CREATED_AT . ' DESC');

        $this->shipmentItems = $collection->getConnection()->fetchAssoc($collection->getSelect());

        return $this->shipmentItems[$orderItemId][ShipmentInterface::CREATED_AT] ?? null;
    }
}
