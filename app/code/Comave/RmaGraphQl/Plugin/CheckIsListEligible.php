<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Plugin;

use Comave\Rma\Service\OrderGracePeriod;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\RmaGraphQl\Model\Resolver\CustomerOrder\EligibleItems;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Psr\Log\LoggerInterface;

class CheckIsListEligible
{
    /**
     * @param OrderGracePeriod $orderGracePeriod
     * @param LoggerInterface $logger
     */
    public function __construct(
        private readonly OrderGracePeriod $orderGracePeriod,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param EligibleItems $isEligibleResolver
     * @param OrderItemInterface[]|array $eligibleReturnItems
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @return OrderItemInterface[]|array
     */
    public function afterResolve(
        EligibleItems $isEligibleResolver,
        array $eligibleReturnItems,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null
    ): array {
        if (!isset($value['model']) && !($value['model'] instanceof OrderInterface)) {
            return $eligibleReturnItems;
        }

        if (empty($eligibleReturnItems)) {
            return $eligibleReturnItems;
        }

        foreach ($eligibleReturnItems as &$eligibleReturnItem) {
            if (!isset($eligibleReturnItem['id'])) {
                continue;
            }

            try {
                $eligibleReturnItem['eligible_for_return'] = $eligibleReturnItem['eligible_for_return'] &&
                    $this->orderGracePeriod->isAllowed(
                        $value['model'],
                        base64_decode($eligibleReturnItem['id'])
                    );
            } catch (\Exception $e) {
                $this->logger->warning(
                    '[ComaveRmaEligibility] Failed checking grace period for RMA, falling back to 14 days from order creation',
                    [
                        'message' => $e->getMessage(),
                        'order' => $value['model']->getIncrementId(),
                    ]
                );

                $eligibleReturnItem['eligible_for_return'] = false;

                continue;
            }
        }

        return $eligibleReturnItems;
    }
}
