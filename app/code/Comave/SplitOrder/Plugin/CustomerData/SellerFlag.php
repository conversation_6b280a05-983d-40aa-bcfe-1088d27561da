<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin\CustomerData;

use Magento\Customer\Helper\Session\CurrentCustomer;
use Webkul\Marketplace\Helper\Data;

class SellerFlag
{
    /**
     * @param Data $mpHelperData
     * @param CurrentCustomer $currentCustomer
     */
    public function __construct(
        private readonly Data $mpHelperData,
        private readonly CurrentCustomer $currentCustomer,
    ) {
    }

    /**
     * @param \Magento\Customer\CustomerData\Customer $customerSectionProvider
     * @param array $result
     * @return array
     */
    public function afterGetSectionData(
        \Magento\Customer\CustomerData\Customer $customerSectionProvider,
        array $result
    ): array {
        if (!$this->currentCustomer->getCustomerId()) {
            return $result;
        }

        return array_merge(
            $result,
            [
                'isSeller' => $this->isSeller(
                    (int) $this->currentCustomer->getCustomerId()
                )
            ]
        );
    }

    /**
     * Check is seller
     * @param int $customerId
     * @return bool
     */
    private function isSeller(int $customerId): bool
    {
        return $this->mpHelperData->getSellerCollectionObj($customerId)
                ->addFieldToFilter('is_seller', true)
                ->getSize() > 0;
    }
}
