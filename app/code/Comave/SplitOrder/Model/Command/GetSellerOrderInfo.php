<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Model\Command;

use Magento\Framework\App\ResourceConnection;
use Magento\Sales\Model\Order\Item;

class GetSellerOrderInfo
{
    private array $loadedData = [];

    public function __construct(
        private readonly ResourceConnection $resourceConnection
    ) {
    }

    /**
     * @param Item $orderItem
     * @return array
     */
    public function get(Item $orderItem): array
    {
        if (isset($this->loadedData[$orderItem->getOrderId()][$orderItem->getId()])) {
            return $this->loadedData[$orderItem->getOrderId()][$orderItem->getId()];
        }

        $connection = $this->resourceConnection->getConnection('read');
        $orderSelectQuery = $connection->select()
            ->from(
                ['sl' => $connection->getTableName('marketplace_saleslist')],
                [
                    'order_item_id',
                    'seller_order_number',
                    'seller_id',
                    'entity_id'
                ]
            )->join(
                ['c' => $connection->getTableName('customer_entity')],
                'c.entity_id = sl.seller_id',
                [
                    'firstname',
                    'lastname'
                ]
            )->where(
                'sl.order_id = ?',
                $orderItem->getOrderId()
            );

        $this->loadedData[$orderItem->getOrderId()] = $connection->fetchAssoc($orderSelectQuery);

        return $this->loadedData[$orderItem->getOrderId()][$orderItem->getId()] ?? [];
    }
}
