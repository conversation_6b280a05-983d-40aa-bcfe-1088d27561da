<?php

declare(strict_types=1);

namespace Comave\ShopifyAccountModifier\Plugin;

use Comave\CategoryMatcher\Model\ConfigProvider;
use Webkul\MpMultiShopifyStoreMageConnect\Block\Adminhtml\ShopifyAccount\Edit\Tabs;

class ChangeTabs
{
    /**
     * @param ConfigProvider $configProvider
     */
    public function __construct(private readonly ConfigProvider $configProvider)
    {
    }

    /**
     * @param Tabs $shopifyTabs
     * @param callable $proceed
     * @param string $tabId
     * @param ...$args
     * @return mixed
     */
    public function aroundAddTab(
        Tabs $shopifyTabs,
        callable $proceed,
        string $tabId,
        ...$args
    ): mixed {
        if ($tabId !== 'mapcategory') {
            return $proceed($tabId, ...$args);
        }

        return $this->configProvider->isEnabled() ? $shopifyTabs : $proceed($tabId, ...$args);
    }
}
