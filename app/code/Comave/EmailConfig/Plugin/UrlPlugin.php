<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Plugin;

use Magento\Framework\Url;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Comave\EmailConfig\Model\Config\Config;

class UrlPlugin
{
    public function __construct(
        private readonly State $appState,
        private readonly Config $frontendConfig
    ) {
    }

    /**
     * Plugin to modify getUrl method for email context
     *
     * @param Url $subject
     * @param string $result
     * @param string|null $routePath
     * @param array|null $routeParams
     * @return string
     */
    public function afterGetUrl(Url $subject, string $result, $routePath = null, $routeParams = null): string
    {
        // Only modify URLs in frontend area (where emails are processed)
        try {
            $currentArea = $this->appState->getAreaCode();
            if ($currentArea !== Area::AREA_FRONTEND) {
                return $result;
            }
        } catch (\Exception $e) {
            return $result;
        }

        // Check if this is a customer-facing route that should use frontend URL
        if ($this->shouldUseFrontendUrl($routePath)) {
            return $this->convertToFrontendUrl($result, $routePath, $routeParams);
        }

        return $result;
    }

    /**
     * Check if the route should use frontend URL
     *
     * @param string|null $routePath
     * @return bool
     */
    private function shouldUseFrontendUrl(?string $routePath): bool
    {
        if (!$routePath) {
            return false;
        }

        // Customer-facing routes that should use frontend URLs
        $customerRoutes = [
            'customer/account',
            'customer/account/',
            'sales/order/history',
            'sales/order/history/',
            'sales/order/view',
            'sales/order/view/',
            'sales/order/creditmemo',
            'sales/order/creditmemo/',
            'checkout/',
            'catalog/'
        ];

        foreach ($customerRoutes as $route) {
            if (strpos($routePath, $route) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Convert backend URL to frontend URL
     *
     * @param string $backendUrl
     * @param string|null $routePath
     * @param array|null $routeParams
     * @return string
     */
    private function convertToFrontendUrl(string $backendUrl, ?string $routePath, ?array $routeParams): string
    {
        // Get frontend base URL
        $frontendBaseUrl = $this->frontendConfig->getFrontendSecureUrl();
        
        if (!$frontendBaseUrl) {
            return $backendUrl;
        }

        // Parse the backend URL to extract the path and query
        $parsedUrl = parse_url($backendUrl);
        
        if (!isset($parsedUrl['path'])) {
            return $backendUrl;
        }

        // Build the frontend URL
        $frontendUrl = rtrim($frontendBaseUrl, '/') . $parsedUrl['path'];
        
        // Add query string if present
        if (isset($parsedUrl['query'])) {
            $frontendUrl .= '?' . $parsedUrl['query'];
        }

        // Add fragment if present
        if (isset($parsedUrl['fragment'])) {
            $frontendUrl .= '#' . $parsedUrl['fragment'];
        }

        return $frontendUrl;
    }
}
