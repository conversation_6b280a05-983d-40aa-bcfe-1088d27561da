<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
/** @var \Magento\Framework\Escaper $escaper */
$selectedSeller = $block->getRequest()->getParam('seller');
/** @var \Comave\SellerOnboarding\ViewModel\ManualMapping $viewModel */
$viewModel = $block->getParentBlock()->getData('manualViewModel');
$categoryList = $viewModel->getCategoryMapping($selectedSeller);
?>
<?php if (empty($categoryList)): ?>
    <p><?= /** @noEscape */ __('You have no categories imported yet'); ?></p>
    <?php return; ?>
<?php endif; ?>

<table class="admin__control-table">
    <thead>
    <tr>
        <th class="col-label"><?= /** @noEscape  */ __('Seller Category'); ?></th>
        <th class="col-options"><?= /** @noEscape  */ __('Magento Category'); ?></th>
        <th class="col-options"><?= /** @noEscape  */ __('Accuracy'); ?></th>
        <th class="col-action"><?= /** @noEscape  */ __('Assign'); ?></th>
    </tr>
    </thead>
    <tbody>
        <?php foreach ($categoryList as $categoryMapping): ?>
            <?php
                $mappingId = $categoryMapping->getData(\Comave\SellerOnboarding\Api\Data\Category\MappingInterface::MAPPING_ID);
                $mappingType = (int) $categoryMapping->getData(\Comave\SellerOnboarding\Api\Data\Category\MappingInterface::MAPPING_TYPE);
                $sourceCategoryName = $categoryMapping->getData(\Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface::SOURCE_CATEGORY_NAME);
                $accuracy = $categoryMapping->getData(\Comave\SellerOnboarding\Api\Data\Category\MappingInterface::MAPPING_ACCURACY);
                $encodedOption = json_encode([
                    'id' => $escaper->escapeHtml($categoryMapping->getData(\Comave\SellerOnboarding\Api\Data\Category\MappingInterface::MAPPING_CATEGORY_ID)),
                    'text' => $escaper->escapeHtml($categoryMapping->getData('name'))
                ]);
            ?>
            <tr>
                <td class="col-label">
                    <label>
                        <?= $escaper->escapeHtml($sourceCategoryName) ?>
                    </label>
                </td>
                <td class="col-options">
                    <select data-option='<?= /** @noEscape */ $encodedOption ?>'
                            data-mapping-id='<?= /** @noEscape */ $mappingId; ?>'
                            name="magento_category[]"
                            multiple class="admin__control-multiselect"></select>
                </td>
                <td class="col-accuracy">
                    <label>
                        <?= $escaper->escapeHtml(number_format($accuracy, 2)) ?>%
                    </label>
                </td>
                <td class="col-action">
                    <button type="button"
                            class="action-icon <?= $mappingType === \Comave\SellerOnboarding\Api\Data\Category\MappingInterface::MAPPING_TYPE_MANUAL ? 'active' : '' ?>"
                            data-row-id="<?= $escaper->escapeHtmlAttr($mappingId) ?>">
                        <svg class="icon-check" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416zm0 464A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0L369 209z"/>
                        </svg>
                    </button>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
<?php
$scriptString = <<<SCRIPTSTRING
require([
    'jquery',
    "Magento_Ui/js/modal/alert",
    'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js'
], function ($, alert) {
    $(document).on('click', '.action-icon', function () {
        if ($(this).hasClass('active')) {
            return false;
        }

        $(this).removeClass('pending').addClass('active');
        $('body').trigger('processStart');
        const selector = $(this).parents('tr').find('select');

        var formData = {
            category_ids: $(this).parents('tr').find('select').val(),
            mapping_id: selector.data('mapping-id')
        };
        $.ajax({
            url: '{$block->getUrl('seller_onboarding/category_mapping/assign')}',
            data: formData,
            type: 'post',
            dataType: 'json',
            success: function (response) {
                $('body').trigger('processStop');
                alert({
                    content: 'Successfully processed the request'
                });
            },
            error: function (response) {
                $('body').trigger('processStop');
                alert({
                    content: 'There was an error processing your request'
                });
            }
        });
    });

    $(document).ready(function() {
        $('.admin__control-multiselect').select2({
            placeholder: "Select categories",
            minimumInputLength: 5,
            allowClear: true,
            ajax: {
                url: '{$block->getUrl('seller_onboarding/category_mapping/search')}',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items, // Assuming your API returns an array of items
                        pagination: {
                            more: data.page < data.total_pages
                        }
                    };
                },
                cache: true
            }
        });

        $('.admin__control-multiselect').each(function (i, input) {
            var data = $(input).data('option');
            var newOption = new Option(data.text, data.id, true, false);

            $(input).append(newOption);
            $(input).val(data.id).trigger('change');
        });

        $('.admin__control-multiselect').on('select2:select', function (e) {
            const icon = $(this).parents('tr').find('.action-icon');
            if (!icon.hasClass('active')) {
                icon.addClass('pending');
            }
        });
    });
});
SCRIPTSTRING;
?>
<?= $secureRenderer->renderTag('script', [], $scriptString, false); ?>
<style>
    .admin__control-table {
        width: 100%;
        border-collapse: collapse;
    }
    .admin__control-table td.col-action {
        text-align: center;
        vertical-align: middle;
    }
    .admin__control-table th, .admin__control-table td {
        padding: 10px;
        text-align: left;
    }
    .admin__control-multiselect {
        width: 100%;
    }
    .action-icon {
        background: transparent;
        border: none;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    .icon-check {
        width: 24px;
        height: 24px;
        fill: dimgrey;
        transition: fill 0.3s ease;
    }
    .action-icon:hover .icon-check,
    .action-icon.active .icon-check {
        fill: lightgreen;
    }
    .action-icon.pending .icon-check {
        fill: orange;
    }
    .action-icon:hover,
    .action-icon.active {
        background: transparent !important;
    }
</style>
