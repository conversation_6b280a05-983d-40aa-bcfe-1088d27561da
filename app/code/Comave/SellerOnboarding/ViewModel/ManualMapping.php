<?php

declare(strict_types=1);

namespace Comave\SellerOnboarding\ViewModel;

use Comave\SellerOnboarding\Api\Data\Category\DataSourceInterface;
use Comave\SellerOnboarding\Api\Data\Category\MappingInterface;
use Magento\Catalog\Ui\Component\Product\Form\Categories\Options;
use Magento\Framework\DB\Select;
use Magento\Framework\Stdlib\ArrayUtils;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Catalog\Model\ResourceModel\Category\Collection as MappingCollection;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as MappingCollectionFactory;
use Webkul\Marketplace\Model\ResourceModel\Seller\Collection;
use Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory;

class ManualMapping implements ArgumentInterface
{
    /**
     * @param Options $categoryOptions
     * @param CollectionFactory $sellerCollectionFactory
     * @param MappingCollectionFactory $mappingCollectionFactory
     */
    public function __construct(
        private readonly Options $categoryOptions,
        private readonly CollectionFactory $sellerCollectionFactory,
        private readonly MappingCollectionFactory $mappingCollectionFactory,
    ) {
    }

    /**
     * @return array
     */
    public function getSellerList(): array
    {
        /** @var Collection $collection */
        $collection = $this->sellerCollectionFactory->create();
        $collection->getSelect()->reset(Select::COLUMNS);
        $collection->getSelect()
            ->join(
                ['c' => $collection->getTable('customer_entity')],
                'c.entity_id = main_table.seller_id',
                [
                    'entity_id',
                    'firstname',
                    'lastname'
                ]
            )->group('seller_id');

        return $collection->getItems();
    }

    /**
     * @param string $sellerId
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getCategoryMapping(string $sellerId): array
    {
        /** @var MappingCollection $collection */
        $collection = $this->mappingCollectionFactory->create();
        $collection->getSelect()->reset(Select::COLUMNS);
        $collection->addAttributeToSelect(['name'], 'inner');
        $collection->getSelect()
            ->join(
                ['sm' => $collection->getTable('comave_seller_onboarding_category_mapping')],
                'sm.mapping_category_id = e.entity_id',
                [
                    MappingInterface::MAPPING_ACCURACY,
                    MappingInterface::MAPPING_ID,
                    MappingInterface::MAPPING_TYPE,
                    MappingInterface::MAPPING_CATEGORY_ID
                ]
            )->join(
                ['s' => $collection->getTable('comave_seller_onboarding_category_data_source')],
                's.source_id = sm.mapping_source_id',
                [
                    DataSourceInterface::SOURCE_CATEGORY_NAME,
                    DataSourceInterface::SOURCE_ID
                ]
            )->where(
                'seller_id = ?',
                $sellerId
            );

        return $collection->getItems();
    }

    /**
     * @return array
     */
    public function getCategoryTree(): array
    {
        return $this->categoryOptions->toOptionArray();
    }
}
