<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Plugin\Adminhtml;

use Comave\SellerApi\Api\IntegrationInterface;
use Comave\SplitOrder\Model\Command\GetSellerOrderInfo;
use Magento\Framework\DataObject;
use Magento\Framework\UrlInterface;
use Magento\Sales\Block\Adminhtml\Items\AbstractItems;
use Magento\Sales\Model\Order\Item;

class AppendSellerOrderRow
{
    /**
     * @param GetSellerOrderInfo $getSellerOrderInfo
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        private readonly GetSellerOrderInfo $getSellerOrderInfo,
        private readonly UrlInterface $urlBuilder
    ) {
    }

    public function afterGetItemHtml(
        AbstractItems $itemsRenderer,
        string $result,
        DataObject $orderItem
    ): string {
        if (!$orderItem instanceof Item) {
            return $result;
        }

        $orderData = $this->getSellerOrderInfo->get($orderItem);

        if (empty($orderData)) {
            return $result;
        }

        $externalLink = '';

        if (isset($orderData['integration']) && $orderData['integration'] instanceof IntegrationInterface) {
            $orderLink = $orderData['integration']->getExternalOrderLink(
                (string) $orderItem->getOrderId()
            );
            $integrationType = ucfirst(
                preg_replace(
                    '/[^a-zA-Z]/',
                    '',
                    $orderData['integration']->getIntegrationType()
                )
            );

            $externalLink = sprintf(
                'Integration: <strong>%s</strong> - <a href="%s" target="_blank">View External Order</a>    |    ',
                $integrationType,
                $orderLink
            );
        }

        $loginAsSellerUrl = $this->urlBuilder->getUrl(
            'loginascustomer/login/login',
            [
                'customer_id' => $orderData['seller_id'],
                'marketplaceOrderId' => $orderData['entity_id']
            ]
        );
        $onClick = 'window.lacConfirmationPopup(\''
            . $loginAsSellerUrl .
        '\')';
        $sellerName = sprintf('%s %s', $orderData['firstname'], $orderData['lastname']);
        $sellerRow = <<<SELLERROW
<tr>
<td>{$externalLink}Seller: <strong>$sellerName - #{$orderData['seller_order_number']}</strong> - <a href="#" onclick="$onClick">View Marketplace Order</a></td>
</tr>
SELLERROW;

        return $sellerRow . $result;
    }
}
