<?php

declare(strict_types=1);

namespace Webkul\Marketplace\Service;

use Magento\Customer\Model\Customer;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;

class UserInfoService
{
    /**
     * @var string[]|array
     */
    private array $loadedEntities = [];

    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(private readonly CollectionFactory $collectionFactory)
    {}

    /**
     * @param int $productId
     * @param int $orderId
     * @return array{id: string, uid?: string, name: string}|array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get(int $productId, int $orderId): array
    {
        $loadedKey = sprintf('%s-%s', $productId, $orderId);

        if (isset($this->loadedEntities[$loadedKey])) {
            return $this->loadedEntities[$loadedKey];
        }

        $collection = $this->collectionFactory->create();
        $collection->getSelect()
            ->reset(\Magento\Framework\DB\Select::COLUMNS)
            ->columns(['entity_id', 'firstname', 'lastname']);

        $collection->addAttributeToSelect('commave_uuid', 'left');
        $collection->getSelect()
            ->join(
                ['so' => $collection->getTable('marketplace_saleslist')],
                '`so`.seller_id = `e`.entity_id',
                ''
            )->where(
                'order_id = ?',
                $orderId
            )->where(
                'mageproduct_id = ?',
                $productId
            )->group('seller_id');

        if (!$collection->getSize()) {
            return [];
        }

        /** @var Customer $customer */
        $customer = $collection->getFirstItem();

        $this->loadedEntities[$loadedKey] = [
            'id' => $customer->getId(),
            'name' => $customer->getName(),
            'uid' => $customer->getData('commave_uuid')
        ];

        return $this->loadedEntities[$loadedKey];
    }
}
