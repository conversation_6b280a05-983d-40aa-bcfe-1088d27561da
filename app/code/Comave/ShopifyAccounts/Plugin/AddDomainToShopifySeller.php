<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Plugin;

use Comave\SellerApi\Api\IntegrationInterface;

class AddDomainToShopifySeller
{
    /**
     * @param IntegrationInterface $integration
     * @param string|null $externalOrderLink
     * @return string
     */
    public function afterGetExternalOrderLink(
        IntegrationInterface $integration,
        ?string $externalOrderLink
    ): string {
        if (empty($externalOrderLink)) {
            return $externalOrderLink;
        }

        $extension = $integration->getExtensionAttributes();

        if (!$extension?->getShopifyAccount()) {
            return $externalOrderLink;
        }

        return sprintf(
            'https://%s%s',
            $extension->getShopifyAccount()->getShopifyDomainName(),
            $externalOrderLink
        );
    }
}
