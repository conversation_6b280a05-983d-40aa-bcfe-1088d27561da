<?php

declare(strict_types=1);

namespace Comave\SplitOrder\Observer;

use Comave\SellerApi\Model\IntegrationTypePool;
use Comave\SplitOrder\Api\SellerCartDetailsInterface;
use Magento\Catalog\Model\Product;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class SellerData implements ObserverInterface
{
    /**
     * @param IntegrationTypePool $integrationTypePool
     */
    public function __construct(
        private readonly IntegrationTypePool $integrationTypePool
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $transport = $observer->getTransport();

        if (!$transport instanceof \stdClass || !property_exists($transport, 'options')) {
            return;
        }

        $product = $observer->getProduct();

        if (!$product instanceof Product) {
            return;
        }

        try {
            $integration = $this->integrationTypePool->identifyIntegration(
                (string) $product->getId()
            );
            $transport->options[SellerCartDetailsInterface::SELLER_OPTION] = json_encode([
                'seller_id' => $integration->getSellerId(),
                'integration_type' => $integration->getIntegrationType(),
            ]);
        } catch (\Exception $e) {
            //add logging
        }
    }
}
