<?php
declare(strict_types=1);

namespace Comave\EmailConfig\Model\Config;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\DeploymentConfig;

class Config
{
    private const string XML_PATH_FRONTEND_UNSECURED_URL = 'web/unsecure/frontend_base_url';
    private const string XML_PATH_FRONTEND_SECURED_URL = 'web/secure/frontend_base_url';

    public function __construct(
        private readonly ScopeConfigInterface $storeConfig,
        private readonly DeploymentConfig $deploymentConfig
    ) {
    }

    public function getFrontendSecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        // First try to get from deployment config
        $deploymentUrl = $this->deploymentConfig->get('frontend_base_url');
        if ($deploymentUrl) {
            return rtrim($deploymentUrl, '/') . '/';
        }

        // Fallback to store config
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_FRONTEND_SECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }

    public function getFrontendUnsecureUrl($scopeType = 'default', $scopeCode = null): string
    {
        // First try to get from deployment config
        $deploymentUrl = $this->deploymentConfig->get('frontend_base_url');
        if ($deploymentUrl) {
            // For unsecure, convert https to http if needed
            $url = str_replace('https://', 'http://', $deploymentUrl);
            return rtrim($url, '/') . '/';
        }

        // Fallback to store config
        return (string) $this->storeConfig->getValue(
            self::XML_PATH_FRONTEND_UNSECURED_URL,
            $scopeType,
            $scopeCode
        ) ?: '';
    }
}
