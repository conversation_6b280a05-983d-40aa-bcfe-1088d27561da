<?php

declare(strict_types=1);

namespace Comave\EmailConfig\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\UrlInterface;
use Comave\EmailConfig\Model\Config\Config;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;

class TestEmailUrls extends Command
{
    public function __construct(
        private readonly UrlInterface $urlBuilder,
        private readonly Config $config,
        private readonly State $appState
    ) {
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('comave:test-email-urls')
            ->setDescription('Test email URL generation');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            // Set area to frontend to simulate email context
            $this->appState->setAreaCode(Area::AREA_FRONTEND);
            
            $output->writeln('<info>Testing Email URL Generation</info>');
            $output->writeln('');
            
            // Test deployment config URLs
            $output->writeln('<comment>Frontend URLs from deployment config:</comment>');
            $output->writeln('Secure: ' . $this->config->getFrontendSecureUrl());
            $output->writeln('Unsecure: ' . $this->config->getFrontendUnsecureUrl());
            $output->writeln('');
            
            // Test URL generation for customer routes
            $output->writeln('<comment>Generated URLs for customer routes:</comment>');
            $customerAccountUrl = $this->urlBuilder->getUrl('customer/account/', ['_nosid' => 1]);
            $output->writeln('Customer Account: ' . $customerAccountUrl);
            
            $orderHistoryUrl = $this->urlBuilder->getUrl('sales/order/history/', ['_nosid' => 1]);
            $output->writeln('Order History: ' . $orderHistoryUrl);
            
            $orderViewUrl = $this->urlBuilder->getUrl('sales/order/view/', ['order_id' => 123, '_nosid' => 1]);
            $output->writeln('Order View: ' . $orderViewUrl);
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $output->writeln('<error>Error: ' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }
    }
}
